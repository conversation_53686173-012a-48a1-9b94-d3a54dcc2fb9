# apps/traefik/cloudflare-middleware.yaml
# Traefik 中间件配置，用于处理 Cloudflare 代理的头部

apiVersion: traefik.containo.us/v1alpha1
kind: Middleware
metadata:
  name: cloudflare-headers
  namespace: default
spec:
  headers:
    customRequestHeaders:
      # 信任 Cloudflare 的真实 IP 头部
      X-Forwarded-Proto: "https"
    customResponseHeaders:
      # 安全头部
      X-Frame-Options: "DENY"
      X-Content-Type-Options: "nosniff"
      Referrer-Policy: "strict-origin-when-cross-origin"
      # Cloudflare 优化
      CF-Cache-Status: ""
    # 信任 Cloudflare IP 范围
    trustedIPs:
      - "************/20"
      - "************/22"
      - "************/22"
      - "**********/22"
      - "************/18"
      - "*************/18"
      - "************/20"
      - "************/20"
      - "*************/22"
      - "************/17"
      - "***********/15"
      - "**********/13"
      - "**********/14"
      - "**********/13"
      - "**********/22"
