# apps/cert-manager/cloudflare-origin-issuer.yaml
# Cloudflare Origin Certificate ClusterIssuer 配置

apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: cloudflare-origin-issuer
spec:
  ca:
    secretName: cloudflare-origin-ca-secret

---
# Cloudflare Origin CA 证书 Secret
# 注意：这个 Secret 需要手动创建，包含 Cloudflare Origin CA 的私钥和证书
apiVersion: v1
kind: Secret
metadata:
  name: cloudflare-origin-ca-secret
  namespace: cert-manager
type: Opaque
data:
  # 这些值需要从 Cloudflare Dashboard 获取
  # tls.crt: <base64-encoded-origin-certificate>
  # tls.key: <base64-encoded-private-key>
  # 占位符 - 需要替换为实际的证书数据
  tls.crt: LS0tLS1CRUdJTi... # 替换为实际的 Origin Certificate
  tls.key: LS0tLS1CRUdJTi... # 替换为实际的 Private Key
