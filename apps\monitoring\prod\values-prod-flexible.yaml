# apps/monitoring/prod/values-prod-flexible.yaml
# 生产环境配置，使用 Cloudflare Flexible SSL 模式（推荐的简单解决方案）

# Prometheus Operator 配置
prometheusOperator:
  # 确保安装 CRD
  createCustomResource: true
  # 启用 admission webhooks
  admissionWebhooks:
    enabled: true
    patch:
      enabled: true

# K3s 通用配置
kube-dns:
  enabled: false
kubeProxy:
  enabled: false
prometheus-node-exporter:
  hostRootfs: false

# 生产环境特有配置 - Prometheus
prometheus:
  ingress:
    enabled: true
    ingressClassName: "traefik"
    annotations:
      # 移除 cert-manager，使用 HTTP 后端
      # Cloudflare 将处理 SSL 终止
      traefik.ingress.kubernetes.io/redirect-scheme: "https"
      # 添加 Cloudflare 特定的头部
      traefik.ingress.kubernetes.io/router.middlewares: "default-cloudflare-headers@kubernetescrd"
    hosts: [ "prometheus.liuovo.com" ]
    # 移除 TLS 配置，让 Cloudflare 处理 SSL

# Grafana 配置
grafana:
  adminPassword: "prom-operator"
  ingress:
    enabled: true
    ingressClassName: "traefik"
    annotations:
      # 移除 cert-manager，使用 HTTP 后端
      traefik.ingress.kubernetes.io/redirect-scheme: "https"
      # 添加 Cloudflare 特定的头部
      traefik.ingress.kubernetes.io/router.middlewares: "default-cloudflare-headers@kubernetescrd"
    hosts: [ "grafana.liuovo.com" ]
    # 移除 TLS 配置

# AlertManager 配置
alertmanager:
  ingress:
    enabled: true
    ingressClassName: "traefik"
    annotations:
      # 移除 cert-manager，使用 HTTP 后端
      traefik.ingress.kubernetes.io/redirect-scheme: "https"
      # 添加 Cloudflare 特定的头部
      traefik.ingress.kubernetes.io/router.middlewares: "default-cloudflare-headers@kubernetescrd"
    hosts: [ "alertmanager.liuovo.com" ]
    # 移除 TLS 配置
