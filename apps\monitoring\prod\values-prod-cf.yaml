# apps/monitoring/prod/values-prod-cf.yaml
# 生产环境配置，针对 Cloudflare 代理优化

# Prometheus Operator 配置
prometheusOperator:
  # 确保安装 CRD
  createCustomResource: true
  # 启用 admission webhooks
  admissionWebhooks:
    enabled: true
    patch:
      enabled: true

# K3s 通用配置
kube-dns:
  enabled: false
kubeProxy:
  enabled: false
prometheus-node-exporter:
  hostRootfs: false

# 生产环境特有配置 - Prometheus
prometheus:
  ingress:
    enabled: true
    ingressClassName: "traefik"
    annotations:
      # 使用 Cloudflare Origin Certificate
      cert-manager.io/cluster-issuer: "cloudflare-origin-issuer"
      # Cloudflare 优化配置
      traefik.ingress.kubernetes.io/router.tls: "true"
      # 强制 HTTPS 重定向
      traefik.ingress.kubernetes.io/redirect-scheme: "https"
    hosts: [ "prometheus.liuovo.com" ]
    tls:
      - secretName: prometheus-liuovo-com-cf-tls
        hosts: [ "prometheus.liuovo.com" ]

# Grafana 配置 (包含默认密码和生产环境 Ingress)
grafana:
  adminPassword: "prom-operator"
  ingress:
    enabled: true
    ingressClassName: "traefik"
    annotations:
      # 使用 Cloudflare Origin Certificate
      cert-manager.io/cluster-issuer: "cloudflare-origin-issuer"
      # Cloudflare 优化配置
      traefik.ingress.kubernetes.io/router.tls: "true"
      # 强制 HTTPS 重定向
      traefik.ingress.kubernetes.io/redirect-scheme: "https"
    hosts: [ "grafana.liuovo.com" ]
    tls:
      - secretName: grafana-liuovo-com-cf-tls
        hosts: [ "grafana.liuovo.com" ]

# AlertManager 配置
alertmanager:
  ingress:
    enabled: true
    ingressClassName: "traefik"
    annotations:
      # 使用 Cloudflare Origin Certificate
      cert-manager.io/cluster-issuer: "cloudflare-origin-issuer"
      # Cloudflare 优化配置
      traefik.ingress.kubernetes.io/router.tls: "true"
      # 强制 HTTPS 重定向
      traefik.ingress.kubernetes.io/redirect-scheme: "https"
    hosts: [ "alertmanager.liuovo.com" ]
    tls:
      - secretName: alertmanager-liuovo-com-cf-tls
        hosts: [ "alertmanager.liuovo.com" ]
