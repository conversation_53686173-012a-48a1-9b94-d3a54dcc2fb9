# Cloudflare 526 SSL 错误解决方案

## 问题分析

### 1. 错误原因
- **错误代码 526**: "Invalid SSL certificate"
- **根本原因**: Cloudflare 无法验证源服务器的 SSL 证书
- **具体情况**: Cloudflare 设置为 "Full" 或 "Full (strict)" 模式，但后端证书配置有问题

### 2. 当前配置问题
- 使用 Let's Encrypt 证书 + Cloudflare 代理
- Cloudflare 可能无法正确验证 Let's Encrypt 证书
- SSL 模式配置不匹配

## 解决方案

### 方案一：使用 Cloudflare Flexible SSL（推荐）

这是最简单的解决方案，适合大多数场景。

#### 1. Cloudflare 设置
在 Cloudflare Dashboard 中：
1. 进入域名的 SSL/TLS 设置
2. 将 SSL 模式设置为 **"Flexible"**
3. 启用 "Always Use HTTPS"

#### 2. 更新 K3s 配置
使用新的配置文件 `values-prod-flexible.yaml`：

```bash
# 备份当前配置
cp apps/monitoring/prod/values-prod.yaml apps/monitoring/prod/values-prod-backup.yaml

# 使用新的 Flexible SSL 配置
cp apps/monitoring/prod/values-prod-flexible.yaml apps/monitoring/prod/values-prod.yaml

# 应用 Cloudflare 中间件
kubectl apply -f apps/traefik/cloudflare-middleware.yaml

# 提交更改
git add .
git commit -m "fix: 配置 Cloudflare Flexible SSL 模式"
git push
```

#### 3. 工作原理
- **客户端 → Cloudflare**: HTTPS (SSL 由 Cloudflare 处理)
- **Cloudflare → K3s**: HTTP (无需后端 SSL 证书)
- **优点**: 简单、可靠、无证书管理复杂性
- **缺点**: Cloudflare 到源服务器的连接是 HTTP

### 方案二：使用 Cloudflare Origin Certificates（高级）

如果需要端到端加密，使用此方案。

#### 1. 获取 Cloudflare Origin Certificate
1. 登录 Cloudflare Dashboard
2. 进入 SSL/TLS → Origin Server
3. 点击 "Create Certificate"
4. 选择域名：`*.liuovo.com`
5. 下载证书和私钥

#### 2. 创建 Kubernetes Secret
```bash
# 将证书保存为文件
kubectl create secret tls cloudflare-origin-ca-secret \
  --cert=origin-cert.pem \
  --key=origin-key.pem \
  -n cert-manager
```

#### 3. 应用配置
```bash
# 使用 Origin Certificate 配置
cp apps/monitoring/prod/values-prod-cf.yaml apps/monitoring/prod/values-prod.yaml

# 应用 ClusterIssuer
kubectl apply -f apps/cert-manager/cloudflare-origin-issuer.yaml

# 提交更改
git add .
git commit -m "fix: 配置 Cloudflare Origin Certificates"
git push
```

#### 4. Cloudflare 设置
- SSL 模式设置为 **"Full (strict)"**
- 启用 "Always Use HTTPS"

## 验证步骤

### 1. 检查证书状态
```bash
# 检查证书是否正确创建
kubectl get certificates -n monitoring

# 检查 Ingress 状态
kubectl get ingress -n monitoring
```

### 2. 测试访问
```bash
# 测试域名解析
nslookup prometheus.liuovo.com
nslookup grafana.liuovo.com
nslookup alertmanager.liuovo.com

# 测试 HTTP 响应
curl -I https://prometheus.liuovo.com
curl -I https://grafana.liuovo.com
curl -I https://alertmanager.liuovo.com
```

### 3. 浏览器测试
访问以下 URL，确认无 SSL 错误：
- https://prometheus.liuovo.com
- https://grafana.liuovo.com
- https://alertmanager.liuovo.com

## 推荐配置

对于生产环境，推荐使用 **方案一（Flexible SSL）**：
- 简单可靠
- 无证书管理复杂性
- 适合大多数监控场景
- Cloudflare 提供 DDoS 保护和 CDN 加速

如果有严格的端到端加密要求，则使用 **方案二（Origin Certificates）**。
