# apps/monitoring/prod/values-prod.yaml
# 生产环境配置，包含基础配置和生产环境特有配置

# Prometheus Operator 配置
prometheusOperator:
  # 确保安装 CRD
  createCustomResource: true
  # 启用 admission webhooks
  admissionWebhooks:
    enabled: true
    patch:
      enabled: true

# K3s 通用配置
kube-dns:
  enabled: false
kubeProxy:
  enabled: false
prometheus-node-exporter:
  hostRootfs: false

# 生产环境特有配置
prometheus:
  ingress:
    enabled: true
    ingressClassName: "traefik"
    annotations:
      cert-manager.io/cluster-issuer: "letsencrypt-prod"
    hosts: [ "prometheus.liuovo.com" ]
    tls:
      - secretName: prometheus-liuovo-com-tls
        hosts: [ "prometheus.liuovo.com" ]

# Grafana 配置 (包含默认密码和生产环境 Ingress)
grafana:
  adminPassword: "prom-operator"
  ingress:
    enabled: true
    ingressClassName: "traefik"
    annotations:
      cert-manager.io/cluster-issuer: "letsencrypt-prod"
    hosts: [ "grafana.liuovo.com" ]
    tls:
      - secretName: grafana-liuovo-com-tls
        hosts: [ "grafana.liuovo.com" ]

alertmanager:
  ingress:
    enabled: true
    ingressClassName: "traefik"
    annotations:
      cert-manager.io/cluster-issuer: "letsencrypt-prod"
    hosts: [ "alertmanager.liuovo.com" ]
    tls:
      - secretName: alertmanager-liuovo-com-tls
        hosts: [ "alertmanager.liuovo.com" ]
